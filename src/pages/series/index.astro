---
import Layout from '../../layouts/Layout.astro';
import { ThemeProvider } from '../../components/ThemeProvider';
import LayoutWrapper from '../../components/LayoutWrapper';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import SeriesPageClient from '../../components/SeriesPageClient';
import { getAllSeries } from '../../utils/series';

// Get all series data
const allSeries = await getAllSeries();

// SEO data for series listing page
const seoData = {
  title: "Blog Series - Feb<PERSON>",
  description: "Explore comprehensive blog series covering cloud engineering, DevOps, web development, and modern technology topics. Learn through structured, multi-part tutorials and guides.",
  canonical: "https://febryan.web.id/series",
  type: "website",
  keywords: ["blog series", "tutorials", "cloud engineering", "devops", "web development", "kubernetes", "docker", "programming"],
};
---

<Layout
  title={seoData.title}
  description={seoData.description}
  canonical={seoData.canonical}
  type={seoData.type}
  keywords={seoData.keywords}
>
  <ThemeProvider client:load>
    <LayoutWrapper client:load>
      <div class="min-h-screen bg-white dark:bg-neutral-900">
        <Header client:load />
        <SeriesPageClient series={allSeries} client:load />
        <Footer client:load />
      </div>
    </LayoutWrapper>
  </ThemeProvider>
</Layout>
