---
import Layout from '../../layouts/Layout.astro';
import { ThemeProvider } from '../../components/ThemeProvider';
import LayoutWrapper from '../../components/LayoutWrapper';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import SeriesDetailClient from '../../components/SeriesDetailClient';
import { getAllSeries, getSeriesBySlug } from '../../utils/series';

export async function getStaticPaths() {
  const allSeries = await getAllSeries();
  return allSeries.map((series) => ({
    params: { slug: series.slug },
    props: { series },
  }));
}

const { series } = Astro.props;

if (!series) {
  return Astro.redirect('/404');
}

// SEO data for series detail page
const seoData = {
  title: `${series.name} - Blog Series - <PERSON><PERSON>`,
  description: series.description,
  canonical: `https://febryan.web.id/series/${series.slug}`,
  type: "website",
  keywords: [...series.tags, "blog series", "tutorial", "guide"],
  image: series.thumbnail,
};
---

<Layout
  title={seoData.title}
  description={seoData.description}
  canonical={seoData.canonical}
  type={seoData.type}
  keywords={seoData.keywords}
  image={seoData.image}
>
  <ThemeProvider client:load>
    <LayoutWrapper client:load>
      <div class="min-h-screen bg-white dark:bg-neutral-900">
        <Header client:load />
        <SeriesDetailClient series={series} client:load />
        <Footer client:load />
      </div>
    </LayoutWrapper>
  </ThemeProvider>
</Layout>
